package com.ecco.webApi.contacts.occupancy;

import com.ecco.dom.ReportCriteriaDto;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.webApi.contacts.address.AddressHistoryViewModel;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@SuperBuilder
@NoArgsConstructor
public class OccupancyHistoryViewModel extends AddressHistoryViewModel {

    /**
     * The number of days between validFrom and validTo.
     * If validTo is null, calculated using the current date and time.
     */
    public Long days;

    /**
     * Gets the number of days between validFrom and validTo.
     * If validTo is null, uses the current date and time.
     * @return the number of days between validFrom and validTo (or today)
     */
    public static long getDays(OccupancyHistoryViewModel vm) {
        if (vm.validFrom == null) {
            return 0;
        }

        LocalDateTime endDate = vm.validTo != null ? vm.validTo : LocalDateTime.now();
        return ChronoUnit.DAYS.between(vm.validFrom, endDate);
    }

    public static boolean isUnoccupied(OccupancyHistoryViewModel vm) {
        return vm.serviceRecipientId == null;
    }

    public static OccupancyHistoryViewModel createUnoccupiedEntry(Integer buildingId, LocalDateTime validFrom, LocalDateTime validTo) {
        OccupancyHistoryViewModel unoccupied = new OccupancyHistoryViewModel();
        unoccupied.id = null; // No actual database record
        unoccupied.serviceRecipientId = null; // No specific service recipient for gap entries
        unoccupied.validFrom = validFrom;
        unoccupied.validTo = validTo;
        unoccupied.days = getDays(unoccupied);
        //unoccupied.occupancyType = "unoccupied";
        unoccupied.contactId = null;
        unoccupied.buildingId = buildingId;
        unoccupied.addressId = null;
        return unoccupied;
    }

    /**
     * Fill gaps in occupancy history with 'unoccupied' entries.
     * This method identifies periods where there are gaps between validTo and validFrom
     * and inserts placeholder entries with occupancyType = 'unoccupied'.
     * Also creates gaps from the DTO's start date to the first entry and from the last entry to the DTO's end date.
     */
    public static List<OccupancyHistoryViewModel> fillOccupancyGaps(List<OccupancyHistoryViewModel> viewModels, ReportCriteriaDto dto) {
        if (viewModels.isEmpty()) {
            return viewModels;
        }

        // Get the date range from DTO
        LocalDateTime dtoStartDate = dto.getFromDate() != null ? JodaToJDKAdapters.localDateTimeToJDk(dto.getFromDate().toLocalDateTime(org.joda.time.LocalTime.MIDNIGHT)) : null;
        LocalDateTime dtoEndDate = dto.getToDate() != null ? JodaToJDKAdapters.localDateTimeToJDk(dto.getToDate().toLocalDateTime(org.joda.time.LocalTime.MIDNIGHT)) : null;

        // Group by buildingId to handle each building separately
        // Filter out entries with null buildingId as they can't be grouped meaningfully
        Map<Integer, List<OccupancyHistoryViewModel>> groupedByBuilding = viewModels.stream()
                .filter(vm -> vm.buildingId != null)
                .collect(Collectors.groupingBy(vm -> vm.buildingId));

        List<OccupancyHistoryViewModel> result = new ArrayList<>();

        // Add entries with null buildingId directly (no gap filling possible)
        List<OccupancyHistoryViewModel> nullBuildingEntries = viewModels.stream()
                .filter(vm -> vm.buildingId == null)
                .collect(toList());
        result.addAll(nullBuildingEntries);

        for (Map.Entry<Integer, List<OccupancyHistoryViewModel>> entry : groupedByBuilding.entrySet()) {
            Integer buildingId = entry.getKey();
            List<OccupancyHistoryViewModel> buildingHistory = entry.getValue();

            // Sort by validFrom date (ascending)
            buildingHistory.sort(Comparator.comparing(vm -> vm.validFrom));

            List<OccupancyHistoryViewModel> filledHistory = new ArrayList<>();

            // Create gap from DTO start date to first entry if needed
            if (dtoStartDate != null && !buildingHistory.isEmpty()) {
                OccupancyHistoryViewModel firstEntry = buildingHistory.get(0);
                if (firstEntry.validFrom != null && dtoStartDate.isBefore(firstEntry.validFrom)) {
                    OccupancyHistoryViewModel startGap = OccupancyHistoryViewModel.createUnoccupiedEntry(buildingId, dtoStartDate, firstEntry.validFrom);
                    filledHistory.add(startGap);
                }
            }

            for (int i = 0; i < buildingHistory.size(); i++) {
                OccupancyHistoryViewModel current = buildingHistory.get(i);

                // Add the current entry
                filledHistory.add(current);

                // Check if there's a gap to the next entry
                if (i < buildingHistory.size() - 1) {
                    OccupancyHistoryViewModel next = buildingHistory.get(i + 1);
                    LocalDateTime currentEnd = current.validTo;
                    LocalDateTime nextStart = next.validFrom;

                    // If there's a gap (current.validTo is not null and doesn't connect to next.validFrom)
                    if (currentEnd != null && nextStart != null && currentEnd.isBefore(nextStart)) {
                        // Create an 'unoccupied' entry for the gap
                        OccupancyHistoryViewModel gapEntry = OccupancyHistoryViewModel.createUnoccupiedEntry(buildingId, currentEnd, nextStart);
                        filledHistory.add(gapEntry);
                    }
                }
            }

            // Create gap from last entry to DTO end date if needed
            if (dtoEndDate != null && !buildingHistory.isEmpty()) {
                OccupancyHistoryViewModel lastEntry = buildingHistory.get(buildingHistory.size() - 1);
                if (lastEntry.validTo != null && lastEntry.validTo.isBefore(dtoEndDate)) {
                    OccupancyHistoryViewModel endGap = OccupancyHistoryViewModel.createUnoccupiedEntry(buildingId, lastEntry.validTo, dtoEndDate);
                    filledHistory.add(endGap);
                }
            }

            result.addAll(filledHistory);
        }

        // Sort the final result by validFrom
        result.sort(Comparator.comparing((OccupancyHistoryViewModel vm) -> vm.validFrom));

        return result;
    }

}
